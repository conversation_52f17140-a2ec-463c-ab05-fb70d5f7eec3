"""
Supervisor核心实现

负责协调和管理多个专业化Agent的工作流程
"""

from typing import Dict, Any, List, Literal, Optional
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.types import Command
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel, Field
from core.llm import model
from core.state import SupervisorState
from agents import ResearchAgent, MathAgent


class TaskAssignment(BaseModel):
    """任务分配模型"""
    agent: Literal["research_agent", "math_agent", "FINISH"] = Field(
        description="要分配任务的Agent名称，如果任务完成则选择FINISH"
    )
    task_description: str = Field(
        description="详细的任务描述，包含所有相关上下文信息"
    )
    reasoning: str = Field(
        description="选择该Agent的理由"
    )


class SupervisorAgent:
    """
    Supervisor Agent类
    
    负责任务分配、流程控制和Agent协调
    """
    
    def __init__(self):
        """初始化Supervisor"""
        self.name = "supervisor"
        self.available_agents = {
            "research_agent": ResearchAgent(),
            "math_agent": MathAgent()
        }
        self.agent = self._create_supervisor_agent()
    
    def _create_supervisor_agent(self):
        """创建Supervisor Agent实例"""
        # 创建任务分配工具
        handoff_tools = self._create_handoff_tools()
        
        return create_react_agent(
            model=model,
            tools=handoff_tools,
            state_modifier=self._get_system_prompt()
        )
    
    def _create_handoff_tools(self) -> List:
        """创建任务移交工具"""
        tools = []
        
        # 为每个Agent创建移交工具
        for agent_name, agent_instance in self.available_agents.items():
            tool_func = self._create_handoff_tool(agent_name, agent_instance)
            tools.append(tool_func)
        
        return tools
    
    def _create_handoff_tool(self, agent_name: str, agent_instance):
        """创建单个Agent的移交工具"""
        
        @tool(name=f"assign_to_{agent_name}")
        def handoff_tool(
            task_description: str = Field(description="要分配给Agent的详细任务描述")
        ) -> Command:
            """分配任务给指定的Agent"""
            
            # 创建任务消息
            task_message = HumanMessage(content=task_description)
            
            return Command(
                goto=agent_name,
                update={
                    "messages": [task_message],
                    "current_agent": agent_name,
                    "task_description": task_description,
                    "task_status": "assigned"
                }
            )
        
        # 设置工具描述
        handoff_tool.description = f"将任务分配给{agent_instance.description}"
        
        return handoff_tool
    
    def _get_system_prompt(self) -> str:
        """获取Supervisor的系统提示词"""
        agent_descriptions = []
        for name, agent in self.available_agents.items():
            agent_descriptions.append(f"- {name}: {agent.description}")
        
        agents_info = "\n".join(agent_descriptions)
        
        return f"""你是一个多Agent系统的Supervisor，负责协调和管理以下专业化Agent：

{agents_info}

你的主要职责：
1. 分析用户请求，确定需要哪个Agent来处理
2. 将任务分配给最合适的Agent
3. 监控任务执行进度
4. 协调多个Agent之间的协作
5. 整合最终结果并返回给用户

工作原则：
- 一次只分配任务给一个Agent，不要并行调用多个Agent
- 根据任务类型选择最合适的Agent
- 为Agent提供清晰、详细的任务描述
- 不要自己执行具体的工作，而是委托给专业Agent
- 确保任务完成后向用户提供完整的结果

任务分配指南：
- 研究、搜索、数据分析类任务 → research_agent
- 数学计算、统计分析类任务 → math_agent
- 如果任务需要多个Agent协作，请按顺序分配

当所有必要的工作完成后，整合结果并向用户报告。"""
    
    def invoke(self, state: SupervisorState) -> Dict[str, Any]:
        """
        调用Supervisor处理请求
        
        Args:
            state: 当前状态
            
        Returns:
            处理结果
        """
        try:
            response = self.agent.invoke(state)
            return response
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"Supervisor error: {str(e)}")],
                "error": str(e),
                "task_status": "failed"
            }
    
    def stream(self, state: SupervisorState):
        """
        流式处理请求
        
        Args:
            state: 当前状态
            
        Yields:
            处理过程中的中间结果
        """
        try:
            for chunk in self.agent.stream(state):
                yield chunk
        except Exception as e:
            yield {
                "error": str(e),
                "task_status": "failed"
            }
    
    def get_available_agents(self) -> Dict[str, str]:
        """
        获取可用Agent列表
        
        Returns:
            Agent名称和描述的字典
        """
        return {
            name: agent.description 
            for name, agent in self.available_agents.items()
        }
    
    def get_agent_info(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定Agent的详细信息
        
        Args:
            agent_name: Agent名称
            
        Returns:
            Agent信息
        """
        if agent_name in self.available_agents:
            return self.available_agents[agent_name].get_info()
        return None
    
    def add_agent(self, agent_name: str, agent_instance):
        """
        添加新的Agent
        
        Args:
            agent_name: Agent名称
            agent_instance: Agent实例
        """
        self.available_agents[agent_name] = agent_instance
        # 重新创建Supervisor Agent以包含新的工具
        self.agent = self._create_supervisor_agent()
    
    def remove_agent(self, agent_name: str):
        """
        移除Agent
        
        Args:
            agent_name: 要移除的Agent名称
        """
        if agent_name in self.available_agents:
            del self.available_agents[agent_name]
            # 重新创建Supervisor Agent
            self.agent = self._create_supervisor_agent()


# 导出Supervisor类
__all__ = ["SupervisorAgent", "TaskAssignment"]
