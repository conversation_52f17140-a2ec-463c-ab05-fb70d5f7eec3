"""
配置管理模块

管理应用程序的所有配置参数和环境变量
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


@dataclass
class ModelConfig:
    """模型配置类"""
    provider: str = "openai"
    model_name: str = "deepseek-chat"
    temperature: float = 0.0
    max_tokens: Optional[int] = None
    timeout: int = 60

    @property
    def full_model_name(self) -> str:
        """获取完整的模型名称"""
        return f"{self.provider}:{self.model_name}"


@dataclass
class AgentConfig:
    """Agent配置类"""
    max_iterations: int = 10
    timeout: int = 300
    enable_memory: bool = True
    enable_streaming: bool = True


@dataclass
class WorkflowConfig:
    """工作流配置类"""
    enable_checkpointing: bool = True
    max_concurrent_agents: int = 1
    retry_attempts: int = 3
    retry_delay: float = 1.0


@dataclass
class APIConfig:
    """API配置类"""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    cors_origins: list = None

    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = ["*"]


class Config:
    """主配置类"""

    def __init__(self):
        """初始化配置"""
        self._load_config()

    def _load_config(self):
        """从环境变量加载配置"""

        # 模型配置
        self.model = ModelConfig(
            provider=os.getenv("MODEL_PROVIDER", "openai"),
            model_name=os.getenv("MODEL_NAME", "deepseek-chat"),
            temperature=float(os.getenv("MODEL_TEMPERATURE", "0.0")),
            max_tokens=int(os.getenv("MODEL_MAX_TOKENS")) if os.getenv("MODEL_MAX_TOKENS") else None,
            timeout=int(os.getenv("MODEL_TIMEOUT", "60"))
        )

        # Agent配置
        self.agent = AgentConfig(
            max_iterations=int(os.getenv("AGENT_MAX_ITERATIONS", "10")),
            timeout=int(os.getenv("AGENT_TIMEOUT", "300")),
            enable_memory=os.getenv("AGENT_ENABLE_MEMORY", "true").lower() == "true",
            enable_streaming=os.getenv("AGENT_ENABLE_STREAMING", "true").lower() == "true"
        )

        # 工作流配置
        self.workflow = WorkflowConfig(
            enable_checkpointing=os.getenv("WORKFLOW_ENABLE_CHECKPOINTING", "true").lower() == "true",
            max_concurrent_agents=int(os.getenv("WORKFLOW_MAX_CONCURRENT_AGENTS", "1")),
            retry_attempts=int(os.getenv("WORKFLOW_RETRY_ATTEMPTS", "3")),
            retry_delay=float(os.getenv("WORKFLOW_RETRY_DELAY", "1.0"))
        )

        # API配置
        self.api = APIConfig(
            host=os.getenv("API_HOST", "0.0.0.0"),
            port=int(os.getenv("API_PORT", "8000")),
            debug=os.getenv("API_DEBUG", "false").lower() == "true",
            cors_origins=os.getenv("API_CORS_ORIGINS", "*").split(",") if os.getenv("API_CORS_ORIGINS") else ["*"]
        )

        # API密钥
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.openai_base_url = os.getenv("OPENAI_BASE_URL")
        self.tavily_api_key = os.getenv("TAVILY_API_KEY")

        # 验证必要的配置
        self._validate_config()

    def _validate_config(self):
        """验证配置的有效性"""
        errors = []

        # 检查必要的API密钥
        if not self.openai_api_key:
            errors.append("OPENAI_API_KEY is required")

        # 检查数值配置的有效性
        if self.model.temperature < 0 or self.model.temperature > 2:
            errors.append("MODEL_TEMPERATURE must be between 0 and 2")

        if self.agent.max_iterations <= 0:
            errors.append("AGENT_MAX_ITERATIONS must be positive")

        if self.workflow.max_concurrent_agents <= 0:
            errors.append("WORKFLOW_MAX_CONCURRENT_AGENTS must be positive")

        if errors:
            raise ValueError(f"Configuration errors: {'; '.join(errors)}")

    def get_model_kwargs(self) -> Dict[str, Any]:
        """获取模型初始化参数"""
        kwargs = {
            "temperature": self.model.temperature,
            "timeout": self.model.timeout
        }

        if self.model.max_tokens:
            kwargs["max_tokens"] = self.model.max_tokens

        return kwargs

    def get_openai_config(self) -> Dict[str, Any]:
        """获取OpenAI配置"""
        config = {
            "api_key": self.openai_api_key
        }

        if self.openai_base_url:
            config["base_url"] = self.openai_base_url

        return config

    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            "model": {
                "provider": self.model.provider,
                "model_name": self.model.model_name,
                "full_model_name": self.model.full_model_name,
                "temperature": self.model.temperature,
                "max_tokens": self.model.max_tokens,
                "timeout": self.model.timeout
            },
            "agent": {
                "max_iterations": self.agent.max_iterations,
                "timeout": self.agent.timeout,
                "enable_memory": self.agent.enable_memory,
                "enable_streaming": self.agent.enable_streaming
            },
            "workflow": {
                "enable_checkpointing": self.workflow.enable_checkpointing,
                "max_concurrent_agents": self.workflow.max_concurrent_agents,
                "retry_attempts": self.workflow.retry_attempts,
                "retry_delay": self.workflow.retry_delay
            },
            "api": {
                "host": self.api.host,
                "port": self.api.port,
                "debug": self.api.debug,
                "cors_origins": self.api.cors_origins
            }
        }


# 创建全局配置实例
config = Config()

# 导出配置
__all__ = ["config", "Config", "ModelConfig", "AgentConfig", "WorkflowConfig", "APIConfig"]